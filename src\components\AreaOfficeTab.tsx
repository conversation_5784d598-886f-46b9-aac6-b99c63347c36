
import React, { useState } from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { Building2, MapPin, Target } from 'lucide-react';
import chartData from '../data/chartData.json';

const AreaOfficeTab = () => {
  const areaOfficeData = chartData.areaOffice;

  // Filter states
  const [fromDate, setFromDate] = useState<Date>();
  const [toDate, setToDate] = useState<Date>();

  // Column Chart Configuration for Area Office vs Calibrated
  const columnChartOptions = {
    chart: {
      type: 'column',
      height: 400,
      backgroundColor: 'transparent'
    },
    title: {
      text: 'Total No of Area Office vs Area Office Calibrated under MSI',
      style: {
        fontSize: '18px',
        fontWeight: 'bold',
        color: '#1f2937'
      }
    },
    xAxis: {
      categories: areaOfficeData.areaOfficeVsCalibrated.categories,
      title: {
        text: 'Regions',
        style: {
          color: '#6b7280',
          fontWeight: 'bold'
        }
      },
      labels: {
        style: {
          color: '#6b7280'
        },
        rotation: 0
      }
    },
    yAxis: {
      title: {
        text: 'Number of Area Offices',
        style: {
          color: '#6b7280',
          fontWeight: 'bold'
        }
      },
      labels: {
        style: {
          color: '#6b7280'
        }
      },
      gridLineColor: '#e5e7eb'
    },
    legend: {
      align: 'center',
      verticalAlign: 'bottom',
      backgroundColor: 'transparent',
      itemStyle: {
        color: '#374151',
        fontWeight: 'bold'
      }
    },
    plotOptions: {
      column: {
        pointPadding: 0.2,
        borderWidth: 0,
        dataLabels: {
          enabled: true,
          style: {
            color: '#374151',
            fontWeight: 'bold'
          }
        }
      }
    },
    series: areaOfficeData.areaOfficeVsCalibrated.series,
    credits: {
      enabled: false
    },
    tooltip: {
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#d1d5db',
      borderRadius: 8,
      shadow: true
    }
  };

  // Line Chart Configuration for MSI Scores Over Time
  const lineChartOptions = {
    chart: {
      type: 'line',
      height: 400,
      backgroundColor: 'transparent'
    },
    title: {
      text: 'MSI Scores Over Time',
      style: {
        fontSize: '18px',
        fontWeight: 'bold',
        color: '#1f2937'
      }
    },
    xAxis: {
      categories: areaOfficeData.msiScoresOverTime.categories,
      title: {
        text: 'Time Period',
        style: {
          color: '#6b7280',
          fontWeight: 'bold'
        }
      },
      labels: {
        style: {
          color: '#6b7280'
        },
        rotation: 0
      }
    },
    yAxis: {
      title: {
        text: 'MSI Score (%)',
        style: {
          color: '#6b7280',
          fontWeight: 'bold'
        }
      },
      labels: {
        style: {
          color: '#6b7280'
        }
      },
      gridLineColor: '#e5e7eb',
      min: 60,
      max: 100
    },
    legend: {
      align: 'center',
      verticalAlign: 'bottom',
      backgroundColor: 'transparent',
      itemStyle: {
        color: '#374151',
        fontWeight: 'bold'
      }
    },
    plotOptions: {
      line: {
        dataLabels: {
          enabled: true,
          style: {
            color: '#374151',
            fontWeight: 'bold'
          }
        },
        enableMouseTracking: true
      }
    },
    series: areaOfficeData.msiScoresOverTime.series,
    credits: {
      enabled: false
    },
    tooltip: {
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#d1d5db',
      borderRadius: 8,
      shadow: true,
      formatter: function() {
        return `<b>${this.series.name}</b><br/>${this.x}: ${this.y}%`;
      }
    }
  };

  // Create stacked bar chart options for MSI Score categories
  const createStackedBarChartOptions = (title: string, data: any) => {
    const achievedData = data.series[0].data;
    const remainingData = data.series[1].data;
    const maxValues = data.maxValues;

    return {
      chart: {
        type: 'bar',
        height: 600,
        backgroundColor: 'transparent'
      },
      title: {
        text: title,
        style: {
          fontSize: '18px',
          fontWeight: 'bold',
          color: '#1f2937'
        }
      },
      xAxis: {
        categories: data.categories,
        title: {
          text: null
        },
        labels: {
          style: {
            color: '#6b7280',
            fontSize: '12px'
          }
        }
      },
      yAxis: {
        min: 0,
        max: 100,
        title: {
          text: 'Score',
          style: {
            color: '#6b7280',
            fontWeight: 'bold'
          }
        },
        labels: {
          style: {
            color: '#6b7280'
          }
        },
        gridLineColor: '#e5e7eb'
      },
      legend: {
        align: 'center',
        verticalAlign: 'bottom',
        backgroundColor: 'transparent',
        itemStyle: {
          color: '#374151',
          fontWeight: 'bold'
        }
      },
      plotOptions: {
        bar: {
          stacking: 'normal',
          dataLabels: {
            enabled: true,
            inside: true,
            style: {
              color: '#ffffff',
              fontWeight: 'bold',
              textOutline: 'none'
            },
            formatter: function() {
              return this.y > 5 ? this.y : '';
            }
          }
        }
      },
      series: [
        {
          name: 'Maximum',
          data: remainingData,
          color: '#e5e7eb',
          dataLabels: {
            enabled: false
          }
        },
        {
          name: 'Achieved',
          data: achievedData,
          color: '#10b981'
        }
      ],
      credits: {
        enabled: false
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#d1d5db',
        borderRadius: 8,
        shadow: true,
        formatter: function() {
          const maxValue = maxValues ? maxValues[this.point.index] : 100;
          return `<b>${this.series.name}</b><br/>${this.x}: ${this.y}<br/>Max: ${maxValue}`;
        }
      }
    };
  };

  // Create ESG Score stacked column chart options (vertical bars)
  const createESGStackedBarChartOptions = (title: string, data: any) => {
    const achievedData = data.series[0].data;
    const remainingData = data.series[0].data.map((achieved: number, index: number) =>
      data.series[1].data[index] - achieved
    );

    // ESG specific color mapping
    const esgColors = {
      'Environment': '#2c7c69',
      'Social': '#fc6e51',
      'Governance': '#4a90e2',
      'General': '#b0b0b0'
    };

    // Dull/muted versions of ESG colors for remaining values
    const esgDullColors = {
      'Environment': '#a8c5c0',
      'Social': '#feb5a5',
      'Governance': '#a4c4f1',
      'General': '#d8d8d8'
    };

    return {
      chart: {
        type: 'column',
        height: 400,
        backgroundColor: 'transparent'
      },
      title: {
        text: title,
        style: {
          fontSize: '18px',
          fontWeight: 'bold',
          color: '#1f2937'
        }
      },
      xAxis: {
        categories: data.categories,
        title: {
          text: 'ESG Categories',
          style: {
            color: '#6b7280',
            fontWeight: 'bold'
          }
        },
        labels: {
          style: {
            color: '#6b7280',
            fontSize: '12px'
          },
          rotation: 0
        }
      },
      yAxis: {
        min: 0,
        max: 100,
        title: {
          text: 'Score',
          style: {
            color: '#6b7280',
            fontWeight: 'bold'
          }
        },
        labels: {
          style: {
            color: '#6b7280'
          }
        },
        gridLineColor: '#e5e7eb'
      },
      legend: {
        align: 'center',
        verticalAlign: 'bottom',
        backgroundColor: 'transparent',
        itemStyle: {
          color: '#374151',
          fontWeight: 'bold'
        }
      },
      plotOptions: {
        column: {
          stacking: 'normal',
          dataLabels: {
            enabled: true,
            inside: true,
            style: {
              color: '#ffffff',
              fontWeight: 'bold',
              textOutline: 'none'
            },
            formatter: function() {
              return this.y > 5 ? this.y : '';
            }
          }
        }
      },
      series: [
        {
          name: 'Maximum',
          data: remainingData,
          colorByPoint: true,
          colors: data.categories.map((category: string) =>
            esgDullColors[category as keyof typeof esgDullColors] || '#e5e7eb'
          ),
          dataLabels: {
            enabled: false
          }
        },
        {
          name: 'Achieved',
          data: achievedData,
          colorByPoint: true,
          colors: data.categories.map((category: string) =>
            esgColors[category as keyof typeof esgColors] || '#10b981'
          ),
          dataLabels: {
            enabled: true,
            style: {
              color: '#ffffff',
              fontWeight: 'bold'
            },
            formatter: function() {
              return this.y;
            }
          }
        }
      ],
      credits: {
        enabled: false
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#d1d5db',
        borderRadius: 8,
        shadow: true,
        formatter: function() {
          return `<b>${this.series.name}</b><br/>${this.x}: ${this.y}<br/>Max: 100`;
        }
      }
    };
  };

  // Create pie chart options for MSI Grade
  const createPieChartOptions = (title: string, data: any) => {
    return {
      chart: {
        type: 'pie',
        height: 400,
        backgroundColor: 'transparent'
      },
      title: {
        text: title,
        style: {
          fontSize: '18px',
          fontWeight: 'bold',
          color: '#1f2937'
        }
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#d1d5db',
        borderRadius: 8,
        shadow: true,
        pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b><br/>Count: <b>{point.y}</b>'
      },
      accessibility: {
        point: {
          valueSuffix: '%'
        }
      },
      plotOptions: {
        pie: {
          allowPointSelect: true,
          cursor: 'pointer',
          dataLabels: {
            enabled: true,
            format: '<b>{point.name}</b>: {point.percentage:.1f} %',
            style: {
              color: '#374151',
              fontWeight: 'bold'
            }
          },
          showInLegend: true
        }
      },
      legend: {
        align: 'center',
        verticalAlign: 'bottom',
        backgroundColor: 'transparent',
        itemStyle: {
          color: '#374151',
          fontWeight: 'bold'
        }
      },
      series: [{
        name: 'Area Office Count',
        colorByPoint: true,
        data: data.data
      }],
      credits: {
        enabled: false
      }
    };
  };

  // Create management stacked column chart options
  const createManagementStackedColumnChartOptions = (title: string, data: any) => {
    const achievedData = data.series[0].data;
    const remainingData = data.series[0].data.map((achieved: number, index: number) =>
      data.series[1].data[index] - achieved
    );

    // Management specific color mapping
    const managementColors = {
      'Environment Management': '#2c7c69',
      'Social Management': '#fc6e51',
      'Governance Management': '#4a90e2'
    };

    // Dull/muted versions of management colors for remaining values
    const managementDullColors = {
      'Environment Management': '#a8c5c0',
      'Social Management': '#feb5a5',
      'Governance Management': '#a4c4f1'
    };

    // Get colors based on chart title
    const achievedColor = managementColors[title as keyof typeof managementColors] || '#10b981';
    const remainingColor = managementDullColors[title as keyof typeof managementDullColors] || '#e5e7eb';

    return {
      chart: {
        type: 'column',
        height: 400,
        backgroundColor: 'transparent'
      },
      title: {
        text: title,
        style: {
          fontSize: '16px',
          fontWeight: 'bold',
          color: '#1f2937'
        }
      },
      xAxis: {
        categories: data.categories,
        title: {
          text: 'Categories',
          style: {
            color: '#6b7280',
            fontWeight: 'bold'
          }
        },
        labels: {
          style: {
            color: '#6b7280',
            fontSize: '11px'
          },
          rotation: -45
        }
      },
      yAxis: {
        min: 0,
        max: 100,
        title: {
          text: 'Score',
          style: {
            color: '#6b7280',
            fontWeight: 'bold'
          }
        },
        labels: {
          style: {
            color: '#6b7280'
          }
        },
        gridLineColor: '#e5e7eb'
      },
      legend: {
        align: 'center',
        verticalAlign: 'bottom',
        backgroundColor: 'transparent',
        itemStyle: {
          color: '#374151',
          fontWeight: 'bold'
        }
      },
      plotOptions: {
        column: {
          stacking: 'normal',
          dataLabels: {
            enabled: true,
            inside: true,
            style: {
              color: '#ffffff',
              fontWeight: 'bold',
              textOutline: 'none'
            },
            formatter: function() {
              return this.y > 5 ? this.y : '';
            }
          }
        }
      },
      series: [
        {
          name: 'Maximum',
          data: remainingData,
          color: remainingColor,
          dataLabels: {
            enabled: false
          }
        },
        {
          name: 'Achieved',
          data: achievedData,
          color: achievedColor
        }
      ],
      credits: {
        enabled: false
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#d1d5db',
        borderRadius: 8,
        shadow: true,
        formatter: function() {
          return `<b>${this.series.name}</b><br/>${this.x}: ${this.y}<br/>Max: 100`;
        }
      }
    };
  };

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="bg-gradient-to-r from-teal-500 to-teal-600 text-white">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium opacity-90 flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              Area Office Metrics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">59</div>
            <p className="text-teal-100 text-sm mt-1">Total Area Offices across regions</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-cyan-500 to-cyan-600 text-white">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium opacity-90 flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              Calibration Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">78.0%</div>
            <p className="text-cyan-100 text-sm mt-1">Area Offices calibrated under MSI</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-emerald-500 to-emerald-600 text-white">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium opacity-90 flex items-center gap-2">
              <Target className="h-4 w-4" />
              Calibrated Offices
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">46</div>
            <p className="text-emerald-100 text-sm mt-1">Area Offices calibrated under MSI</p>
          </CardContent>
        </Card>
      </div>



      {/* Charts Section */}
      {/* Row 1: Total No of Area Office vs Area Office Calibrated under MSI Chart */}
      <Card className="shadow-lg">
        <CardContent className="p-6">
          <HighchartsReact
            highcharts={Highcharts}
            options={columnChartOptions}
          />
        </CardContent>
      </Card>

      {/* Row 2: MSI Scores Over Time Chart */}
      <Card className="shadow-lg">
        <CardContent className="p-6">
          <HighchartsReact
            highcharts={Highcharts}
            options={lineChartOptions}
          />
        </CardContent>
      </Card>

      {/* Remove this entire Filter Section */}
      {/* Additional Filter Section */}
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-gray-900">Advanced Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4 items-end">
            {/* Zone Filter */}
            <div className="flex flex-col space-y-2 min-w-[150px]">
              <label className="text-sm font-medium text-gray-700">Zone:</label>
              <Select>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select Zone" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Zones</SelectItem>
                  <SelectItem value="east">East Zone</SelectItem>
                  <SelectItem value="north">North Zone</SelectItem>
                  <SelectItem value="south1">South1 Zone</SelectItem>
                  <SelectItem value="south2">South2 Zone</SelectItem>
                  <SelectItem value="west">West Zone</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* City Filter */}
            <div className="flex flex-col space-y-2 min-w-[150px]">
              <label className="text-sm font-medium text-gray-700">City:</label>
              <Select>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select City" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Cities</SelectItem>
                  <SelectItem value="mumbai">Mumbai</SelectItem>
                  <SelectItem value="delhi">Delhi</SelectItem>
                  <SelectItem value="bangalore">Bangalore</SelectItem>
                  <SelectItem value="chennai">Chennai</SelectItem>
                  <SelectItem value="kolkata">Kolkata</SelectItem>
                  <SelectItem value="pune">Pune</SelectItem>
                  <SelectItem value="hyderabad">Hyderabad</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Area Office Name & Code Filter */}
            <div className="flex flex-col space-y-2 min-w-[200px]">
              <label className="text-sm font-medium text-gray-700">Area Office Name & Code:</label>
              <Select>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select Area Office" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Area Offices</SelectItem>
                  <SelectItem value="ao001">AO001 - Central Mumbai</SelectItem>
                  <SelectItem value="ao002">AO002 - South Delhi</SelectItem>
                  <SelectItem value="ao003">AO003 - East Bangalore</SelectItem>
                  <SelectItem value="ao004">AO004 - North Chennai</SelectItem>
                  <SelectItem value="ao005">AO005 - West Kolkata</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Regional Office Filter */}
            <div className="flex flex-col space-y-2 min-w-[200px]">
              <label className="text-sm font-medium text-gray-700">Regional Office:</label>
              <Select>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select Regional Office" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Regional Offices</SelectItem>
                  <SelectItem value="ro001">RO001 - Mumbai Regional</SelectItem>
                  <SelectItem value="ro002">RO002 - Delhi Regional</SelectItem>
                  <SelectItem value="ro003">RO003 - Bangalore Regional</SelectItem>
                  <SelectItem value="ro004">RO004 - Chennai Regional</SelectItem>
                  <SelectItem value="ro005">RO005 - Kolkata Regional</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* From Date Filter */}
            <div className="flex flex-col space-y-2 min-w-[180px]">
              <label className="text-sm font-medium text-gray-700">From Date:</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !fromDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {fromDate ? format(fromDate, "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={fromDate}
                    onSelect={setFromDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* To Date Filter */}
            <div className="flex flex-col space-y-2 min-w-[180px]">
              <label className="text-sm font-medium text-gray-700">To Date:</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !toDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {toDate ? format(toDate, "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={toDate}
                    onSelect={setToDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* Apply Filters Button */}
            <div className="flex flex-col space-y-2 min-w-[200px]">
              <label className="text-sm font-medium text-gray-700 opacity-0">Apply</label>
              <Button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 h-10">
                Apply Advanced Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* MSI Score Categories Section - Single Chart */}
      <Card className="shadow-lg">
        <CardContent className="p-6">
          <HighchartsReact
            highcharts={Highcharts}
            options={createStackedBarChartOptions('MSI Score Categories', areaOfficeData.msiScoreCategories)}
          />
        </CardContent>
      </Card>

      {/* ESG Score and Area Office MSI Grade Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* ESG Score Chart */}
        <Card className="shadow-lg">
          <CardContent className="p-6">
            <HighchartsReact
              highcharts={Highcharts}
              options={createESGStackedBarChartOptions('ESG Score', areaOfficeData.esgScore)}
            />
          </CardContent>
        </Card>

        {/* Area Office MSI Grade Pie Chart */}
        <Card className="shadow-lg">
          <CardContent className="p-6">
            <HighchartsReact
              highcharts={Highcharts}
              options={createPieChartOptions('Area Office MSI Grade', areaOfficeData.msiGrade)}
            />
          </CardContent>
        </Card>
      </div>

      {/* Management Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Environment Management */}
        <Card className="shadow-lg">
          <CardContent className="p-6">
            <HighchartsReact
              highcharts={Highcharts}
              options={createManagementStackedColumnChartOptions('Environment Management', areaOfficeData.environmentManagement)}
            />
          </CardContent>
        </Card>

        {/* Social Management */}
        <Card className="shadow-lg">
          <CardContent className="p-6">
            <HighchartsReact
              highcharts={Highcharts}
              options={createManagementStackedColumnChartOptions('Social Management', areaOfficeData.socialManagement)}
            />
          </CardContent>
        </Card>

        {/* Governance Management */}
        <Card className="shadow-lg">
          <CardContent className="p-6">
            <HighchartsReact
              highcharts={Highcharts}
              options={createManagementStackedColumnChartOptions('Governance Management', areaOfficeData.governanceManagement)}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AreaOfficeTab;

