
import React, { useState } from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import chartData from '../data/chartData.json';

const DealersTab = () => {
  const dealersData = chartData.dealers;
  
  // Filter states
  const [fromDate, setFromDate] = useState<Date>();
  const [toDate, setToDate] = useState<Date>();

  // Column Chart Configuration
  const columnChartOptions = {
    chart: {
      type: 'column',
      height: 400,
      backgroundColor: 'transparent'
    },
    title: {
      text: 'Total No. of Dealers vs Dealers Calibrated under MSI',
      style: {
        fontSize: '18px',
        fontWeight: 'bold',
        color: '#1f2937'
      }
    },
    xAxis: {
      categories: dealersData.dealersVsCalibrated.categories,
      title: {
        text: 'Regions',
        style: {
          color: '#6b7280',
          fontWeight: 'bold'
        }
      },
      labels: {
        style: {
          color: '#6b7280'
        },
        rotation: 0
      }
    },
    yAxis: {
      title: {
        text: 'Number of Dealers',
        style: {
          color: '#6b7280',
          fontWeight: 'bold'
        }
      },
      labels: {
        style: {
          color: '#6b7280'
        }
      },
      gridLineColor: '#e5e7eb'
    },
    legend: {
      align: 'center',
      verticalAlign: 'bottom',
      backgroundColor: 'transparent',
      itemStyle: {
        color: '#374151',
        fontWeight: 'bold'
      }
    },
    plotOptions: {
      column: {
        pointPadding: 0.2,
        borderWidth: 0,
        dataLabels: {
          enabled: true,
          style: {
            color: '#374151',
            fontWeight: 'bold'
          }
        }
      }
    },
    series: dealersData.dealersVsCalibrated.series,
    credits: {
      enabled: false
    },
    tooltip: {
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#d1d5db',
      borderRadius: 8,
      shadow: true
    }
  };

  // Line Chart Configuration
  const lineChartOptions = {
    chart: {
      type: 'line',
      height: 400,
      backgroundColor: 'transparent'
    },
    title: {
      text: 'MSI Scores Over Time with MSI Self Assessment Score and MSI Calibration Score',
      style: {
        fontSize: '18px',
        fontWeight: 'bold',
        color: '#1f2937'
      }
    },
    xAxis: {
      categories: dealersData.msiScoresOverTime.categories,
      title: {
        text: 'Time Period',
        style: {
          color: '#6b7280',
          fontWeight: 'bold'
        }
      },
      labels: {
        style: {
          color: '#6b7280'
        },
        rotation: 0
      }
    },
    yAxis: {
      title: {
        text: 'MSI Score (%)',
        style: {
          color: '#6b7280',
          fontWeight: 'bold'
        }
      },
      labels: {
        style: {
          color: '#6b7280'
        }
      },
      gridLineColor: '#e5e7eb',
      min: 60,
      max: 100
    },
    legend: {
      align: 'center',
      verticalAlign: 'bottom',
      backgroundColor: 'transparent',
      itemStyle: {
        color: '#374151',
        fontWeight: 'bold'
      }
    },
    plotOptions: {
      line: {
        dataLabels: {
          enabled: true,
          style: {
            color: '#374151',
            fontWeight: 'bold',
            fontSize: '10px'
          }
        },
        marker: {
          radius: 6
        }
      }
    },
    series: dealersData.msiScoresOverTime.series.map((series) => ({
      ...series,
      marker: {
        radius: 6,
        fillColor: series.color
      }
    })),
    credits: {
      enabled: false
    },
    tooltip: {
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#d1d5db',
      borderRadius: 8,
      shadow: true,
      formatter: function() {
        return `<b>${this.series.name}</b><br/>${this.x}: <b>${this.y}%</b>`;
      }
    }
  };

  // MSI Chart Configuration
  const msiChartOptions = {
    chart: {
      type: 'column',
      height: 350,
      backgroundColor: 'transparent'
    },
    title: {
      text: 'MSI Chart',
      style: {
        fontSize: '18px',
        fontWeight: 'bold',
        color: '#1f2937'
      }
    },
    xAxis: {
      categories: dealersData.msiChart.categories,
      title: {
        text: 'Performance Level',
        style: {
          color: '#6b7280',
          fontWeight: 'bold'
        }
      },
      labels: {
        style: {
          color: '#6b7280'
        },
        rotation: 0
      }
    },
    yAxis: {
      title: {
        text: 'Count',
        style: {
          color: '#6b7280',
          fontWeight: 'bold'
        }
      },
      labels: {
        style: {
          color: '#6b7280'
        }
      },
      gridLineColor: '#e5e7eb'
    },
    legend: {
      align: 'center',
      verticalAlign: 'bottom',
      backgroundColor: 'transparent',
      itemStyle: {
        color: '#374151',
        fontWeight: 'bold'
      }
    },
    plotOptions: {
      column: {
        pointPadding: 0.2,
        borderWidth: 0,
        dataLabels: {
          enabled: true,
          style: {
            color: '#374151',
            fontWeight: 'bold'
          }
        }
      }
    },
    series: dealersData.msiChart.series,
    credits: {
      enabled: false
    },
    tooltip: {
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#d1d5db',
      borderRadius: 8,
      shadow: true
    }
  };

  // MSI Grade Configuration - Pie Chart with Percentages
  const calculateMsiGradePercentages = () => {
    const salesData = dealersData.msiGrade.series[0].data; // Sale data
    const serviceData = dealersData.msiGrade.series[1].data; // Service data
    const categories = dealersData.msiGrade.categories;

    // Combine sales and service data for each category
    const combinedData = categories.map((category, index) => {
      const totalCount = salesData[index] + serviceData[index];
      return {
        name: category,
        actualCount: totalCount
      };
    });

    // Calculate total count across all categories
    const grandTotal = combinedData.reduce((sum, item) => sum + item.actualCount, 0);

    // Create pie chart data with percentages
    const pieData = combinedData.map((item, index) => ({
      name: item.name,
      y: (item.actualCount / grandTotal) * 100,
      color: ['#D3D3D3', '#FFD700', '#C0C0C0', '#FF4500'][index], // Platinum (light gray), Gold (gold), Silver (silver), Not Met (red-orange)
      actualCount: item.actualCount
    }));

    return pieData;
  };

  const msiGradeOptions = {
    chart: {
      type: 'pie',
      height: 400,
      backgroundColor: 'transparent'
    },
    title: {
      text: 'MSI Grade Distribution',
      style: {
        fontSize: '18px',
        fontWeight: 'bold',
        color: '#1f2937'
      }
    },
    tooltip: {
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#d1d5db',
      borderRadius: 8,
      shadow: true,
      formatter: function() {
        return `<b>${this.point.name}</b><br/>
                Percentage: <b>${this.point.percentage.toFixed(1)}%</b><br/>
                Count: <b>${this.point.actualCount}</b>`;
      }
    },
    legend: {
      align: 'right',
      verticalAlign: 'middle',
      layout: 'vertical',
      backgroundColor: 'transparent',
      itemStyle: {
        color: '#374151',
        fontWeight: 'bold'
      }
    },
    plotOptions: {
      pie: {
        allowPointSelect: true,
        cursor: 'pointer',
        dataLabels: {
          enabled: true,
          format: '<b>{point.name}</b>: {point.percentage:.1f}%',
          style: {
            color: '#374151',
            fontWeight: 'bold',
            fontSize: '11px'
          }
        },
        showInLegend: true
      }
    },
    series: [{
      name: 'MSI Grade Distribution',
      colorByPoint: true,
      data: calculateMsiGradePercentages()
    }],
    credits: {
      enabled: false
    }
  };

  // Helper function to create chart options
  const createBarChartOptions = (title: string, data: any) => ({
    chart: {
      type: 'column',
      height: 350,
      backgroundColor: 'transparent'
    },
    title: {
      text: title,
      style: {
        fontSize: '18px',
        fontWeight: 'bold',
        color: '#1f2937'
      }
    },
    xAxis: {
      categories: data.categories,
      title: {
        text: 'Categories',
        style: {
          color: '#6b7280',
          fontWeight: 'bold'
        }
      },
      labels: {
        style: {
          color: '#6b7280'
        },
        rotation: 0
      }
    },
    yAxis: {
      title: {
        text: 'Score',
        style: {
          color: '#6b7280',
          fontWeight: 'bold'
        }
      },
      labels: {
        style: {
          color: '#6b7280'
        }
      },
      gridLineColor: '#e5e7eb',
      max: 100
    },
    legend: {
      align: 'center',
      verticalAlign: 'bottom',
      backgroundColor: 'transparent',
      itemStyle: {
        color: '#374151',
        fontWeight: 'bold'
      }
    },
    plotOptions: {
      column: {
        pointPadding: 0.2,
        borderWidth: 0,
        dataLabels: {
          enabled: true,
          style: {
            color: '#374151',
            fontWeight: 'bold'
          }
        }
      }
    },
    series: data.series,
    credits: {
      enabled: false
    },
    tooltip: {
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#d1d5db',
      borderRadius: 8,
      shadow: true
    }
  });

  // Helper function to create stacked bar chart options for ESG Score
  const createStackedBarChartOptions = (title: string, data: any) => {
    // Transform data for stacking - calculate remaining values
    const achievedSeries = data.series.find((s: any) => s.name === 'Achieved');
    const maxSeries = data.series.find((s: any) => s.name === 'Maximum');

    const remainingData = achievedSeries.data.map((achieved: number, index: number) => {
      const remaining = maxSeries.data[index] - achieved;
      return remaining > 0 ? remaining : 0; // Ensure no negative values
    });

    // Calculate the maximum value for the y-axis
    const maxValue = Math.max(...maxSeries.data);

    // Check if this is an ESG chart, Environment chart, Social chart, Governance chart, or General chart and apply specific colors
    const isEsgChart = title.includes('ESG Score');
    const isEnvironmentChart = title.includes('Environment');
    const isSocialChart = title.includes('Social');
    const isGovernanceChart = title.includes('Governance');
    const isGeneralChart = title.includes('General');

    // ESG specific color mapping
    const esgColors = {
      'Environment': '#2c7c69',
      'Social': '#fc6e51',
      'Governance': '#4a90e2',
      'General': '#b0b0b0'
    };

    // Dull/muted versions of ESG colors for remaining values
    const esgDullColors = {
      'Environment': '#a8c5c0',
      'Social': '#feb5a5',
      'Governance': '#a4c4f1',
      'General': '#d8d8d8'
    };

    // Update x-axis labels for ESG charts to match the image
    const updatedCategories = isEsgChart ?
      data.categories.map((cat: string) => {
        if (cat === 'Environment') return 'Environm...';
        if (cat === 'Governance') return 'Governance';
        return cat;
      }) : data.categories;

    // Create series data
    const remainingSeriesData = {
      name: 'Maximum',
      data: remainingData,
      color: (isEsgChart || isEnvironmentChart || isSocialChart || isGovernanceChart || isGeneralChart) ? undefined : '#b0b0b0', // Will be overridden for ESG, Environment, Social, Governance, and General charts
      dataLabels: {
        enabled: false
      },
      // For ESG charts, apply dull category-specific colors to each point
      ...(isEsgChart && {
        colorByPoint: true,
        colors: data.categories.map((category: string) =>
          esgDullColors[category as keyof typeof esgDullColors] || '#d8d8d8'
        )
      }),
      // For Environment charts, use dull Environment color for all bars
      ...(isEnvironmentChart && {
        color: esgDullColors['Environment'] // Use dull Environment color
      }),
      // For Social charts, use dull Social color for all bars
      ...(isSocialChart && {
        color: esgDullColors['Social'] // Use dull Social color
      }),
      // For Governance charts, use dull Governance color for all bars
      ...(isGovernanceChart && {
        color: esgDullColors['Governance'] // Use dull Governance color
      }),
      // For General charts, use dull General color for all bars
      ...(isGeneralChart && {
        color: esgDullColors['General'] // Use dull General color
      })
    };

    const achievedSeriesData = {
      name: 'Achieved',
      data: achievedSeries.data,
      color: (isEsgChart || isEnvironmentChart || isSocialChart || isGovernanceChart || isGeneralChart) ? undefined : '#10b981', // Will be overridden for ESG, Environment, Social, Governance, and General charts
      dataLabels: {
        enabled: true,
        style: {
          color: '#ffffff',
          fontWeight: 'bold'
        },
        formatter: function() {
          return this.y;
        }
      },
      // For ESG charts, apply category-specific colors to each point
      ...(isEsgChart && {
        colorByPoint: true,
        colors: data.categories.map((category: string) =>
          esgColors[category as keyof typeof esgColors] || '#10b981'
        )
      }),
      // For Environment charts, use Environment color for all bars
      ...(isEnvironmentChart && {
        color: esgColors['Environment'] // Use Environment color #2c7c69
      }),
      // For Social charts, use Social color for all bars
      ...(isSocialChart && {
        color: esgColors['Social'] // Use Social color #fc6e51
      }),
      // For Governance charts, use Governance color for all bars
      ...(isGovernanceChart && {
        color: esgColors['Governance'] // Use Governance color #4a90e2
      }),
      // For General charts, use General color for all bars
      ...(isGeneralChart && {
        color: esgColors['General'] // Use General color #b0b0b0
      })
    };

    return {
      chart: {
        type: 'column',
        height: 350,
        backgroundColor: 'transparent'
      },
      title: {
        text: title,
        style: {
          fontSize: '18px',
          fontWeight: 'bold',
          color: '#1f2937'
        }
      },
      xAxis: {
        categories: updatedCategories,
        title: {
          text: 'Categories',
          style: {
            color: '#6b7280',
            fontWeight: 'bold'
          }
        },
        labels: {
          style: {
            color: '#6b7280'
          },
          rotation: 0
        }
      },
      yAxis: {
        title: {
          text: 'Score',
          style: {
            color: '#6b7280',
            fontWeight: 'bold'
          }
        },
        labels: {
          style: {
            color: '#6b7280'
          }
        },
        gridLineColor: '#e5e7eb',
        max: maxValue,
        stackLabels: {
          enabled: false
        }
      },
      legend: {
        align: 'center',
        verticalAlign: 'bottom',
        backgroundColor: 'transparent',
        itemStyle: {
          color: '#374151',
          fontWeight: 'bold'
        }
      },
      plotOptions: {
        column: {
          stacking: 'normal',
          pointPadding: 0.2,
          borderWidth: 0,
          dataLabels: {
            enabled: true,
            style: {
              color: '#374151',
              fontWeight: 'bold'
            }
          }
        }
      },
      series: [remainingSeriesData, achievedSeriesData],
      credits: {
        enabled: false
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#d1d5db',
        borderRadius: 8,
        shadow: true,
        formatter: function() {
          const total = this.point.stackTotal;
          const percentage = ((this.y / total) * 100).toFixed(1);
          return `<b>${this.series.name}</b><br/>
                  ${this.x}: <b>${this.y}</b> (${percentage}%)<br/>
                  Total: <b>${total}</b>`;
        }
      }
    };
  };

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium opacity-90">Total Dealers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">2,125</div>
            <p className="text-blue-100 text-sm mt-1">Across all regions</p>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium opacity-90">Calibrated Dealers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">1,670</div>
            <p className="text-green-100 text-sm mt-1">Under MSI program</p>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium opacity-90">Latest MSI Score</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">94%</div>
            <p className="text-purple-100 text-sm mt-1">December 2024</p>
          </CardContent>
        </Card>
      </div>

     
      

      {/* Total No of Dealers vs Dealers Calibrated under MSI - Full Width */}
      <Card className="shadow-lg">
        <CardContent className="p-6">
          <HighchartsReact
            highcharts={Highcharts}
            options={columnChartOptions}
          />
        </CardContent>
      </Card>

       {/* Filters Section */}
      <Card className="shadow-lg mb-6">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-gray-900">Filters</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Zone</label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Select Zone" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="north">North</SelectItem>
                  <SelectItem value="south">South</SelectItem>
                  <SelectItem value="east">East</SelectItem>
                  <SelectItem value="west">West</SelectItem>
                  <SelectItem value="central">Central</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">City</label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Select City" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="mumbai">Mumbai</SelectItem>
                  <SelectItem value="delhi">Delhi</SelectItem>
                  <SelectItem value="bangalore">Bangalore</SelectItem>
                  <SelectItem value="chennai">Chennai</SelectItem>
                  <SelectItem value="kolkata">Kolkata</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Dealer Name</label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Select Dealer" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="dealer1">Dealer A</SelectItem>
                  <SelectItem value="dealer2">Dealer B</SelectItem>
                  <SelectItem value="dealer3">Dealer C</SelectItem>
                  <SelectItem value="dealer4">Dealer D</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Area Office</label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Select Office" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="office1">Office A</SelectItem>
                  <SelectItem value="office2">Office B</SelectItem>
                  <SelectItem value="office3">Office C</SelectItem>
                  <SelectItem value="office4">Office D</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">From Date</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !fromDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {fromDate ? format(fromDate, "PPP") : <span>From date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={fromDate}
                    onSelect={setFromDate}
                    initialFocus
                    className="pointer-events-auto"
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">To Date</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !toDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {toDate ? format(toDate, "PPP") : <span>To date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={toDate}
                    onSelect={setToDate}
                    initialFocus
                    className="pointer-events-auto"
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* MSI Scores Over Time - Full Width */}
      <Card className="shadow-lg">
        <CardContent className="p-6">
          <HighchartsReact
            highcharts={Highcharts}
            options={lineChartOptions}
          />
        </CardContent>
      </Card>

      {/* Additional Chart - Full Width */}
      {/* <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-gray-900">Regional Performance Overview</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-2">Best Performing Region</h3>
              <p className="text-2xl font-bold text-blue-600">North1</p>
              <p className="text-sm text-blue-700">320 total dealers</p>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="font-semibold text-green-900 mb-2">Highest MSI Calibration</h3>
              <p className="text-2xl font-bold text-green-600">North1</p>
              <p className="text-sm text-green-700">250 calibrated dealers</p>
            </div>
            <div className="bg-purple-50 p-4 rounded-lg">
              <h3 className="font-semibold text-purple-900 mb-2">Calibration Rate</h3>
              <p className="text-2xl font-bold text-purple-600">78.6%</p>
              <p className="text-sm text-purple-700">Overall average</p>
            </div>
          </div>
        </CardContent>
      </Card> */}

     

      {/* MSI Chart and MSI Grade - Same Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="shadow-lg">
          <CardContent className="p-6">
            <HighchartsReact 
              highcharts={Highcharts} 
              options={msiChartOptions} 
            />
          </CardContent>
        </Card>

        <Card className="shadow-lg">
          <CardContent className="p-6">
            <HighchartsReact 
              highcharts={Highcharts} 
              options={msiGradeOptions} 
            />
          </CardContent>
        </Card>
      </div>

      {/* Row 1: ESG Score Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="shadow-lg">
          <CardContent className="p-6">
            <HighchartsReact
              highcharts={Highcharts}
              options={createStackedBarChartOptions('ESG Score', dealersData.esgScore)}
            />
          </CardContent>
        </Card>

        <Card className="shadow-lg">
          <CardContent className="p-6">
            <HighchartsReact
              highcharts={Highcharts}
              options={createStackedBarChartOptions('Sales - ESG Score', dealersData.salesEsgScore)}
            />
          </CardContent>
        </Card>

        <Card className="shadow-lg">
          <CardContent className="p-6">
            <HighchartsReact
              highcharts={Highcharts}
              options={createStackedBarChartOptions('Service - ESG Score', dealersData.serviceEsgScore)}
            />
          </CardContent>
        </Card>
      </div>

      {/* Row 2: Environment Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="shadow-lg">
          <CardContent className="p-6">
            <HighchartsReact
              highcharts={Highcharts}
              options={createStackedBarChartOptions('Sales - Environment', dealersData.salesEnvironment)}
            />
          </CardContent>
        </Card>

        <Card className="shadow-lg">
          <CardContent className="p-6">
            <HighchartsReact
              highcharts={Highcharts}
              options={createStackedBarChartOptions('Service - Environment', dealersData.serviceEnvironment)}
            />
          </CardContent>
        </Card>
      </div>

      {/* Row 3: Social Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="shadow-lg">
          <CardContent className="p-6">
            <HighchartsReact
              highcharts={Highcharts}
              options={createStackedBarChartOptions('Sales - Social', dealersData.salesSocial)}
            />
          </CardContent>
        </Card>

        <Card className="shadow-lg">
          <CardContent className="p-6">
            <HighchartsReact
              highcharts={Highcharts}
              options={createStackedBarChartOptions('Service - Social', dealersData.serviceSocial)}
            />
          </CardContent>
        </Card>
      </div>

      {/* Row 4: Governance Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="shadow-lg">
          <CardContent className="p-6">
            <HighchartsReact
              highcharts={Highcharts}
              options={createStackedBarChartOptions('Sales - Governance', dealersData.salesGovernance)}
            />
          </CardContent>
        </Card>

        <Card className="shadow-lg">
          <CardContent className="p-6">
            <HighchartsReact
              highcharts={Highcharts}
              options={createStackedBarChartOptions('Service - Governance', dealersData.serviceGovernance)}
            />
          </CardContent>
        </Card>
      </div>

      {/* Row 5: General Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="shadow-lg">
          <CardContent className="p-6">
            <HighchartsReact
              highcharts={Highcharts}
              options={createStackedBarChartOptions('Sales - General', dealersData.salesGeneral)}
            />
          </CardContent>
        </Card>

        <Card className="shadow-lg">
          <CardContent className="p-6">
            <HighchartsReact
              highcharts={Highcharts}
              options={createStackedBarChartOptions('Service - General', dealersData.serviceGeneral)}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default DealersTab;
